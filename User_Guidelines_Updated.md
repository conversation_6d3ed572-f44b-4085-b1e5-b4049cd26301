# 用户协作指南 (User Guidelines) - 更新版

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 @mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
### 实现工具: @mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## ACE (Augment Context Engine) ACE(codebase-retrieval) 强制使用规则
### 实现工具: @codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与**现有代码**相关的任务时，必须首先调用 ACE，无任何例外
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时
- 在对**任何**现有代码进行编辑、重构或删除之前
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况 (可不调用 ACE)
- **从零创建:** 创建全新的、完全独立的、不与现有代码库集成的全新代码

## 深度分析工具使用 (Sequential Thinking)
### 实现工具: @Sequential thinking

### 分析工具触发条件
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型
- 问题诊断: 复杂bug分析和解决方案设计
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

### 分析工具组合使用
- 用分步骤思考工具思考寻找问题所在，过程中用ACE验证，如此往复协助分析问题

## 文件系统操作工具选择规则 (更新版)
### 实现工具: `@desktop-commander` 和 `str-replace-editor`

### 核心工具选择逻辑
- **默认优先**: `@desktop-commander` 作为文件系统操作的首选工具
- **精确编辑例外**: 当需要进行精确的字符串替换和文件编辑时，可以使用 `str-replace-editor`
- **用户指定**: 当用户明确要求使用特定工具时，优先满足用户需求

### 工具使用场景分配

#### @desktop-commander 适用场景
- 文件创建、读取、写入操作
- 目录创建、列表、搜索操作
- 文件系统相关的查询和管理操作
- 大块内容的文件写入

#### str-replace-editor 适用场景
- 精确的字符串查找和替换
- 需要指定行号范围的编辑操作
- 复杂的文件内容修改
- 用户明确要求使用时

### 工具选择决策流程
1. **用户明确指定** → 使用用户指定的工具
2. **精确编辑需求** → 考虑使用 `str-replace-editor`
3. **一般文件操作** → 使用 `@desktop-commander`
4. **工具不可用** → 使用备用工具并说明原因

### 例外情况处理
- 仅当指定工具明确报错、不可用或功能不支持时，才使用备用工具
- 使用备用工具时必须在回复中说明原因和具体情况

## 工作流程

### 信息收集阶段 (必须执行)
1. **ACE收集** (如涉及现有代码) → 获取代码库上下文
2. **Context 7 强制调研** → 查询将要使用的组件、库、框架用法 (编写代码前必须) → Context 7找不到时使用Web Tools(逛互联网获取知识)补充
3. **澄清优先原则** → 遇到不确定技术细节时:
   - 使用Context 7查询相关文档
   - 使用Web Tools(逛互联网获取知识)获取最新信息
   - 向用户明确询问具体需求
4. **编写代码** → 基于调研结果实现功能

#### 开发禁止行为
- ❌ 不允许基于记忆编写代码
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过Context7调研步骤
- ❌ 不允许在不确定的情况下继续开发

### 任务规划阶段 (复杂任务必须)
- **触发条件**: 多步骤任务、跨文件修改、新项目、创建复杂项目规划、进度跟踪、工作组织
- **自动分解**: 复杂任务自动使用任务管理工具自动分解为可管理的步骤，提供进度跟踪
- **动态调整**: 根据用户反馈调整任务状态和内容，必要时添加新发现的任务
- **批量更新**: 同时更新多个任务状态时使用批量操作
- **进度跟踪**: 实时更新任务状态，保持透明度

### 核心执行规则

#### 强制规则 (必须遵循)
- **文件系统操作** → 根据场景选择 @desktop-commander 或 str-replace-editor
- **涉及现有代码** → 必须先调用 @codebase-retrieval (ACE)
- **开发任务** → 必须先调用 @Context 7 进行技术调研
- **不确定时** → 澄清优先 (Context7/Web Tools/用户询问)
- **每次回复** → 必须调用 @mcp-feedback-enhanced

#### 执行原则
- **工具灵活性**: 根据具体需求和用户偏好选择最适合的工具
- **智能判断**: 在遵循强制规则的前提下，AI根据具体情况灵活选择最佳工具组合
- **质量优先**: 关注结果质量而非流程机械性
- **用户体验**: 提供自然、高效的交互体验

### 测试验证阶段 (按需选择执行)
- 效率优先: 除非特别说明，否则不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据指令生成和修改代码
- 按需服务: 只有用户明确要求时才进行测试、文档、编译、运行等操作

## 高级交互与协作模式

### 核心要求
- 遇到真正复杂的问题时，主动使用深度分析工具进行深度分析
- 在需要时主动询问澄清性问题
- 独立任务可以并行执行
